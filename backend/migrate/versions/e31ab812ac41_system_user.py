"""system user

Revision ID: e31ab812ac41
Revises:
Create Date: 2025-05-27 18:44:31.970770

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e31ab812ac41"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "system_users",
        sa.Column("username", sa.String(), nullable=False),
        sa.Column("email", sa.String(), nullable=True),
        sa.Column("password", sa.String(), nullable=False),
        sa.Column("is_active", sa.<PERSON>an(), nullable=True),
        sa.Column("is_superuser", sa.<PERSON>(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_system_users_email"), "system_users", ["email"], unique=True)
    op.create_index(op.f("ix_system_users_id"), "system_users", ["id"], unique=False)
    op.create_index(op.f("ix_system_users_username"), "system_users", ["username"], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_system_users_username"), table_name="system_users")
    op.drop_index(op.f("ix_system_users_id"), table_name="system_users")
    op.drop_index(op.f("ix_system_users_email"), table_name="system_users")
    op.drop_table("system_users")
    # ### end Alembic commands ###
