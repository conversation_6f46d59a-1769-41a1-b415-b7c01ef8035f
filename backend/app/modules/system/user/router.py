from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from app.modules.system.user.deps import get_current_user

from app.core.database import get_db
from app.modules.system.user.schemas import Token, User, UserCreate, UserLogin
from app.modules.system.user.service import UserService

router = APIRouter()


@router.post("/register/", response_model=User, summary="用户注册")
async def register(params: UserCreate, db: AsyncSession = Depends(get_db)):
    """
    用户注册接口

    - username: 用户名
    - password: 密码
    - email: 邮箱（可选）
    - is_active: 是否激活（默认为True）

    返回创建的用户信息
    """
    return await UserService.create(db, params)


@router.post("/login/", response_model=Token, summary="用户登录")
async def login(params: UserLogin, db: AsyncSession = Depends(get_db)):
    """
    用户登录接口

    - username: 用户名
    - password: 密码

    返回JWT令牌
    """
    user = await UserService.authenticate(db, params.username, params.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"Authorization": "Token"},
        )
    return UserService.create_token(user)


@router.get("/info/", response_model=User, summary="获取用户信息")
async def get_user_info(db: AsyncSession = Depends(get_db), user: User = Depends(get_current_user)):
    """
    获取当前登录用户的信息

    返回用户信息
    """
    return user
